# Beco Location Search MCP Server

This application serves as both a traditional Spring Boot REST API and a Model Context Protocol (MCP) server, providing location search functionality to external services.

## Architecture Overview

The application maintains a clean architecture with:
- **REST API**: Traditional HTTP endpoints for web applications
- **MCP Server**: Auto-configured MCP server capabilities for AI agents and other services
- **Shared Business Logic**: Both interfaces use the same underlying services

## Current Implementation Status

### ✅ **Completed Features**
- **Spring Boot REST API**: Fully functional with Category, Location, and Tool controllers
- **MCP Server Auto-Configuration**: Successfully enabled with all capabilities
- **MongoDB Integration**: Connected to MongoDB Atlas with repositories and services
- **MapStruct Integration**: Controllers use MapStruct for DTO conversion
- **Clean Architecture**: Services return domain objects, controllers handle presentation

### 🔄 **MCP Server Configuration**
- **Server Port**: `http://localhost:8081`
- **MCP Auto-Configuration**: Enabled with tools, resources, prompts, and completions capabilities
- **Transport**: Server-Sent Events (SSE) - Auto-configured by Spring AI
- **Protocol**: Model Context Protocol (MCP)

### Available Tools

#### `beco_location_search`
Search for locations using various criteria including text search, category filtering, tag filtering, and geospatial search.

**Parameters:**
- `query` (string, optional): Text search for location names
- `category` (string array, optional): Filter by category IDs
- `tags` (string array, optional): Filter by tags
- `lat` (number, optional): Latitude for geospatial search
- `lng` (number, optional): Longitude for geospatial search
- `radius` (number, optional): Search radius in meters for geospatial search
- `siteId` (string, optional): Filter by site ID

**Response Format:**
```json
{
  "success": true,
  "locations": [...],
  "count": 42,
  "searchCriteria": {
    "query": "conference room",
    "category": ["meeting-rooms"],
    "tags": ["projector", "whiteboard"],
    "geospatial": false,
    "siteId": "building-a"
  }
}
```

## Usage Examples

### Connecting to MCP Server

#### Using MCP Client Library
```javascript
import { McpClient } from '@modelcontextprotocol/client';

const client = new McpClient({
  transport: 'sse',
  url: 'http://localhost:8080/mcp'
});

await client.connect();

// Discover available tools
const tools = await client.listTools();
console.log('Available tools:', tools);

// Use the location search tool
const result = await client.callTool('beco_location_search', {
  query: 'conference room',
  category: ['meeting-rooms'],
  tags: ['projector']
});

console.log('Search results:', result);
```

#### Using HTTP SSE Directly
```bash
# Connect to MCP server via SSE
curl -N -H "Accept: text/event-stream" \
  -H "Cache-Control: no-cache" \
  "http://localhost:8080/mcp"
```

### Example Tool Calls

#### Text Search
```json
{
  "method": "tools/call",
  "params": {
    "name": "beco_location_search",
    "arguments": {
      "query": "conference room"
    }
  }
}
```

#### Category and Tag Filtering
```json
{
  "method": "tools/call",
  "params": {
    "name": "beco_location_search",
    "arguments": {
      "category": ["meeting-rooms", "offices"],
      "tags": ["projector", "whiteboard"]
    }
  }
}
```

#### Geospatial Search
```json
{
  "method": "tools/call",
  "params": {
    "name": "beco_location_search",
    "arguments": {
      "lat": 40.7128,
      "lng": -74.0060,
      "radius": 1000
    }
  }
}
```

## REST API Endpoints (Fully Functional)

The application provides traditional REST API endpoints:

- `GET /categories` - Get all categories
- `GET /categories/{siteId}` - Get categories by site
- `GET /categories/{siteId}/{categoryId}` - Get specific category
- `GET /locations` - Get all locations
- `GET /locations/{siteId}` - Get locations by site
- `GET /locations/{siteId}/{locationId}` - Get specific location
- `GET /tools/locations/search` - Advanced location search

## Running the Application

### Start the Server
```bash
mvn spring-boot:run
```

The application will start on port 8081 and provide both:
- REST API endpoints at `http://localhost:8081/`
- MCP server capabilities (auto-configured)

### Verify REST API
```bash
# Test categories endpoint
curl http://localhost:8081/categories

# Test locations endpoint
curl http://localhost:8081/locations

# Test advanced search
curl "http://localhost:8081/tools/locations/search?query=conference"
```

### Verify MCP Server Auto-Configuration
Check the application logs for MCP server initialization:
```
INFO o.s.a.m.s.a.McpServerAutoConfiguration : Enable tools capabilities, notification: true
INFO o.s.a.m.s.a.McpServerAutoConfiguration : Enable resources capabilities, notification: true
INFO o.s.a.m.s.a.McpServerAutoConfiguration : Enable prompts capabilities, notification: true
INFO o.s.a.m.s.a.McpServerAutoConfiguration : Enable completions capabilities
```

## Integration with AI Agents

This MCP server can be integrated with various AI agents and tools that support the Model Context Protocol:

1. **Claude Desktop**: Add as a custom MCP server
2. **Custom AI Applications**: Use MCP client libraries
3. **Workflow Automation**: Integrate with automation tools that support MCP

## Error Handling

The MCP tool provides comprehensive error handling:

- **Validation Errors**: Invalid parameters return structured error responses
- **Business Logic Errors**: Database or service errors are caught and returned safely
- **Geospatial Validation**: Ensures complete geospatial parameters or none at all

## Security Considerations

- The MCP server runs on the same port as the REST API
- Consider implementing authentication for production use
- Rate limiting may be needed for high-traffic scenarios
- CORS configuration may be required for browser-based MCP clients
