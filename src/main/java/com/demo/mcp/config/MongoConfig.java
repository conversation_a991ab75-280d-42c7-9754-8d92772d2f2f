package com.demo.mcp.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexOperations;

@Slf4j
@Configuration
public class MongoConfig {
    
    @Autowired
    private MongoTemplate mongoTemplate;
    
    /**
     * Create indexes for better query performance
     * This will run after the application starts
     */
    @Bean
    public CommandLineRunner createIndexes() {
        return args -> {
            try {
                createLocationIndexes();
                createCategoryIndexes();
                log.info("MongoDB indexes created successfully");
            } catch (Exception e) {
                log.error("Error creating MongoDB indexes", e);
            }
        };
    }
    
    /**
     * Create indexes for Location collection
     */
    private void createLocationIndexes() {
        IndexOperations locationIndexOps = mongoTemplate.indexOps("locations");

        // Index on locationId for faster lookups
        locationIndexOps.ensureIndex(new Index().on("locationId", org.springframework.data.domain.Sort.Direction.ASC));

        // Index on siteId for filtering by site
        locationIndexOps.ensureIndex(new Index().on("siteId", org.springframework.data.domain.Sort.Direction.ASC));

        // Text index on name and description for text search
        locationIndexOps.ensureIndex(new Index().on("name", org.springframework.data.domain.Sort.Direction.ASC));
        locationIndexOps.ensureIndex(new Index().on("description", org.springframework.data.domain.Sort.Direction.ASC));

        // Index on categories array for category filtering
        locationIndexOps.ensureIndex(new Index().on("categories", org.springframework.data.domain.Sort.Direction.ASC));

        // Index on tags array for tag filtering
        locationIndexOps.ensureIndex(new Index().on("tags", org.springframework.data.domain.Sort.Direction.ASC));

        // Compound index on node coordinates for geospatial queries
        locationIndexOps.ensureIndex(new Index()
                .on("node.lat", org.springframework.data.domain.Sort.Direction.ASC)
                .on("node.lng", org.springframework.data.domain.Sort.Direction.ASC));

        // Index on hidden field for filtering visible locations
        locationIndexOps.ensureIndex(new Index().on("hidden", org.springframework.data.domain.Sort.Direction.ASC));

        log.debug("Location collection indexes created");
    }
    
    /**
     * Create indexes for Category collection
     */
    private void createCategoryIndexes() {
        IndexOperations categoryIndexOps = mongoTemplate.indexOps("categories");

        // Index on categoryId for faster lookups
        categoryIndexOps.ensureIndex(new Index().on("categoryId", org.springframework.data.domain.Sort.Direction.ASC));

        // Index on siteId for filtering by site
        categoryIndexOps.ensureIndex(new Index().on("siteId", org.springframework.data.domain.Sort.Direction.ASC));

        // Index on name for text search
        categoryIndexOps.ensureIndex(new Index().on("name", org.springframework.data.domain.Sort.Direction.ASC));

        // Index on sortOrder for ordered retrieval
        categoryIndexOps.ensureIndex(new Index().on("sortOrder", org.springframework.data.domain.Sort.Direction.ASC));

        log.debug("Category collection indexes created");
    }
}
