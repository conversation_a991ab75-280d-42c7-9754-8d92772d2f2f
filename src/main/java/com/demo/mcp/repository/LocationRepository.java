package com.demo.mcp.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import com.demo.mcp.model.document.Location;

@Repository
public interface LocationRepository extends MongoRepository<Location, String> {
    
    /**
     * Find location by locationId
     * @param locationId the location ID
     * @return Optional containing the location if found
     */
    Optional<Location> findByLocationId(String locationId);
    
    /**
     * Find all locations by siteId
     * @param siteId the site ID
     * @return List of locations for the site
     */
    List<Location> findBySiteId(String siteId);
    
    /**
     * Find locations by name containing the given text (case insensitive)
     * @param name the name to search for
     * @return List of matching locations
     */
    List<Location> findByNameContainingIgnoreCase(String name);
    
    /**
     * Find locations by categories containing any of the given category IDs
     * @param categoryIds list of category IDs to search for
     * @return List of matching locations
     */
    List<Location> findByCategoriesIn(List<String> categoryIds);
    
    /**
     * Find locations by tags containing any of the given tags
     * @param tags list of tags to search for
     * @return List of matching locations
     */
    List<Location> findByTagsIn(List<String> tags);
    
    /**
     * Find locations within a certain distance from a point
     * Note: This requires geospatial indexing on the node coordinates
     * @param point the center point for search
     * @param distance the maximum distance
     * @return List of locations within the specified distance
     */
    @Query("{ 'node.lat': { $exists: true }, 'node.lng': { $exists: true } }")
    List<Location> findByNodeExists();
    
    /**
     * Find locations by multiple criteria for advanced search
     * @param name name pattern (optional)
     * @param categoryIds category IDs (optional)
     * @param tags tags (optional)
     * @return List of matching locations
     */
    @Query("{ $and: [ " +
           "{ $or: [ { 'name': { $regex: ?0, $options: 'i' } }, { ?0: null } ] }, " +
           "{ $or: [ { 'categories': { $in: ?1 } }, { ?1: null } ] }, " +
           "{ $or: [ { 'tags': { $in: ?2 } }, { ?2: null } ] } " +
           "] }")
    List<Location> findBySearchCriteria(String name, List<String> categoryIds, List<String> tags);
    
    /**
     * Check if location exists by locationId
     * @param locationId the location ID
     * @return true if exists, false otherwise
     */
    boolean existsByLocationId(String locationId);
}
