package com.demo.mcp.repository;

import com.demo.mcp.model.document.Category;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CategoryRepository extends MongoRepository<Category, String> {
    
    /**
     * Find category by categoryId
     * @param categoryId the category ID
     * @return Optional containing the category if found
     */
    Optional<Category> findByCategoryId(String categoryId);
    
    /**
     * Find all categories by siteId
     * @param siteId the site ID
     * @return List of categories for the site
     */
    List<Category> findBySiteId(String siteId);
    
    /**
     * Find categories by name containing the given text (case insensitive)
     * @param name the name to search for
     * @return List of matching categories
     */
    List<Category> findByNameContainingIgnoreCase(String name);
    
    /**
     * Check if category exists by categoryId
     * @param categoryId the category ID
     * @return true if exists, false otherwise
     */
    boolean existsByCategoryId(String categoryId);
}
