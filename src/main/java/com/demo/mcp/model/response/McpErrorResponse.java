package com.demo.mcp.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class McpErrorResponse {
    
    private boolean success = false;
    private Error error;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Error {
        private int code;
        private String message;
        private Map<String, Object> validation;
    }
    
    public static McpErrorResponse validationError(int code, String message, Map<String, Object> validation) {
        return new McpErrorResponse(false, new Error(code, message, validation));
    }
    
    public static McpErrorResponse error(int code, String message) {
        return new McpErrorResponse(false, new Error(code, message, null));
    }
}
