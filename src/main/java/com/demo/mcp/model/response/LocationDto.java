package com.demo.mcp.model.response;


import com.demo.mcp.model.document.Amenity;
import com.demo.mcp.model.document.Location;
import com.demo.mcp.model.document.OperationHour;
import lombok.Data;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;

@Data
public class LocationDto {

    private String locationId;
    private String externalId;
    private String name;
    private Location.Type type;
    private String description;
    private boolean hidden;
    private int sortOrder;
    private Amenity amenity;
    private HashMap<String, String> phone;
    private HashMap<String, String> social;
    private List<OperationHour> operationHours;
    private List<Location.Link> links;
    private List<Location.SiblingGroup> siblingGroups;
    private String zoneName;
    private boolean showSmartLabelWhenImagePresent;
    private List<String> categories;
    private Instant createdDateTime;
    private Instant updatedDateTime;
    private String address;
    private String nodeId;
    private String polygonId;
    private String stateId;
    private List<String> tags;
    private String logoUrl;
    private String bannerUrl;
    private String zoneId;
    private boolean topLocation;
    private boolean showLogo;
    private int minZoom;
    private Location.MapObject mapObject;

}
