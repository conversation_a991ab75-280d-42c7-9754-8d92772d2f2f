package com.demo.mcp.model.response;

import com.demo.mcp.model.document.Location;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class McpLocationSearchResponse {
    
    private boolean success;
    private List<Location> locations;
    private int count;
    private SearchCriteria searchCriteria;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SearchCriteria {
        private String query;
        private List<String> category;
        private List<String> tags;
        private boolean geospatial;
        private Double lat;
        private Double lng;
        private Double radius;
    }
    
    public static McpLocationSearchResponse success(List<Location> locations, SearchCriteria criteria) {
        return new McpLocationSearchResponse(true, locations, locations.size(), criteria);
    }
}
