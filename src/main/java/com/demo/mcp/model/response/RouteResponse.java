package com.demo.mcp.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class RouteResponse implements Serializable {

    private static final long serialVersionUID = 1L;
    private int status;
    private List<NodeDto> nodes;
    private String message;

    @Getter
    public enum RouteStatus {
        SUCCESS(200),
        FAILED(404),
        IN_PROGRESS(409);

        private final int code;

        RouteStatus(int code) {
            this.code = code;
        }
    }

}
