package com.demo.mcp.model.response;


import com.demo.mcp.model.document.Category;
import lombok.Data;

import java.time.Instant;

@Data
public class CategoryDto {

    private String categoryId;
    private String name;
    private Instant createdDateTime;
    private Instant updatedDateTime;
    private String iconFromDefaultList;
    private Integer sortOrder;
    private Category.Color color;
    private String externalId;
    private String siteId;

}
