package com.demo.mcp.model.document;


import com.demo.mcp.model.core.DocumentEntity;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;
import java.util.List;
import java.util.Objects;

import static java.lang.String.format;

@Data
@Document(collection = "nodes")
public class Node implements DocumentEntity<String> {

    @Id
    private String nodeId;
    private double lat;
    private double lng;
    private Instant createdDateTime;
    private Instant updatedDateTime;
    private List<String> siblings;
    private boolean accessible;
    private boolean privateNode;
    private String floorId;
    private String siteId;

    @Override
    public String getId() {
        return nodeId;
    }

    @Override
    public String friendlyName() {
        return format("BeCo Node with id '%s'", this.getId());
    }

    @Override
    public Instant getDateCreated() {
        return createdDateTime;
    }

    @Override
    public void setDateCreated(final Instant dateCreated) {
        this.createdDateTime = dateCreated;
    }

    @Override
    public Instant getLastUpdated() {
        return updatedDateTime;
    }

    @Override
    public void setLastUpdated(final Instant lastUpdated) {
        this.updatedDateTime = lastUpdated;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Node node = (Node) o;
        return Double.compare(node.lat, lat) == 0 &&
                Double.compare(node.lng, lng) == 0;
    }

    @Override
    public int hashCode() {
        return Objects.hash(lat, lng);
    }

}
