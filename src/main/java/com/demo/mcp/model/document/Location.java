package com.demo.mcp.model.document;


import com.demo.mcp.model.core.DocumentEntity;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;

import static java.lang.String.format;

@Data
@Document(collection = "locations")
public class Location implements DocumentEntity<String> {

    @Id
    private String locationId;
    private String externalId;
    private String name;
    private Type type;
    private String description;
    private boolean hidden;
    private int sortOrder;
    private Amenity amenity;
    private HashMap<String, String> phone;
    private HashMap<String, String> social;
    private List<OperationHour> operationHours;
    private List<Link> links;
    private List<SiblingGroup> siblingGroups;
    private boolean showSmartLabelWhenImagePresent;
    private String address;
    private String logo;
    private String banner;
    private boolean topLocation;
    private boolean showLogo;
    private int minZoom;
    private String zoneName;
    private MapObject mapObject;

    private Instant createdDateTime;
    private Instant updatedDateTime;

    private String siteId;
    private String zoneId;
    private String polygonId;
    private String stateId;

    @Transient
    private String nodeId;

    @DBRef(lazy = true)
    private Node node;

    private List<String> categories;
    private List<String> tags;

    @Override
    public Instant getDateCreated() {
        return createdDateTime;
    }

    @Override
    public void setDateCreated(final Instant dateCreated) {
        this.createdDateTime = dateCreated;
    }

    @Override
    public Instant getLastUpdated() {
        return updatedDateTime;
    }

    @Override
    public void setLastUpdated(final Instant lastUpdated) {
        this.updatedDateTime = lastUpdated;
    }

    @Override
    public String getId() {
        return locationId;
    }

    @Override
    public String friendlyName() {
        return format("BeCo Location with id '%s'", this.getId());
    }

    public enum Type {
        TENANT,
        AMENITIES,
        PARKING,
        SEATING,
        GATE,
        SECURITY_CHECKPOINT,
        BUILDING,
        ENTRANCE,
        SHUTTLE,
        KIOSK,
        MAP_OBJECT
    }

    public enum ImageType {
        LOGO,
        BANNER
    }

    @Data
    public static class Link implements Serializable {

        private String url;
        private String label;

    }

    @Data
    public static class SiblingGroup implements Serializable {

        private SiblingType label;
        private List<String> siblings;

        enum SiblingType {
            REFERENCE_POINT,
            CURBSIDE_PICKUP,
        }

    }

    @Data
    public static class MapObject implements Serializable {

        private String modelPath;
        private double scale;
        private double rotation;
        private double modelAltitude;
        private double modelLight;

    }

}
