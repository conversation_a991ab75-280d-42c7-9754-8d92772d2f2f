package com.demo.mcp.model.document;

public enum Amenity {
    RESTROOM("restroom"),
    PRAYER_ROOM("prayer-room"),
    WASHROOM_MEN("washroom-men"),
    WASHROOM_WOMEN("washroom-women"),
    ATM("atm"),
    FOOD_COURT("food-court"),
    INFORMATION_DESK("information-desk"),
    PICKUP_POINT("pickup-point"),
    WASHROOM_FAMILY("washroom-family"),
    WASHROOM_ACCESSIBLE("washroom-accessible"),
    FEEDING_ROOM("feeding-room"),
    TAXI_STAND("taxi-stand"),
    PREPAID_TAXI("prepaid-taxi"),
    BUS_TERMINAL("bus-terminal"),
    METRO_EXIT("metro-exit"),
    DRINKING_WATER("drinking-water"),
    FIRST_AID("first-aid"),
    CHECK_IN_COUNTER("check-in-counter"),
    BAGGAGE_BELT("baggage-belt"),
    BAGGAGE_COUNTER("baggage-counter"),
    LOCKE<PERSON>("locker"),
    CURRENCY_EXCHANGE("currency-exchange"),
    WALLET_PARKING("wallet-parking"),
    RECEPTION("reception"),
    HELP_DESK("help-desk"),
    BILLING_COUNTER("billing-counter"),
    PRAM("pram"),
    ELEVATOR("elevator"),
    VENDING_MACHINE("vending-machine");

    private final String displayName;

    Amenity(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}

