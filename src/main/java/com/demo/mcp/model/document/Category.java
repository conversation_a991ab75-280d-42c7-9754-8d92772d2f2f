package com.demo.mcp.model.document;


import com.demo.mcp.model.core.DocumentEntity;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.time.Instant;

import static java.lang.String.format;

@Data
@Document(collection = "categories")
public class Category implements DocumentEntity<String> {

    @Id
    private String categoryId;
    private String name;
    private Instant createdDateTime;
    private Instant updatedDateTime;
    private String iconFromDefaultList;
    private Integer sortOrder;
    private Color color;
    private String externalId;
    private String siteId;

    @Override
    public Instant getDateCreated() {
        return createdDateTime;
    }

    @Override
    public void setDateCreated(final Instant dateCreated) {
        this.createdDateTime = dateCreated;
    }

    @Override
    public Instant getLastUpdated() {
        return updatedDateTime;
    }

    @Override
    public void setLastUpdated(final Instant lastUpdated) {
        this.updatedDateTime = lastUpdated;
    }

    @Override
    public String getId() {
        return categoryId;
    }

    @Override
    public String friendlyName() {
        return format("BeCo Category with id '%s'", this.getId());
    }

    @Data
    public static class Color implements Serializable {

        private String rgba;
        private String hex;
        private double opacity;

    }

}
