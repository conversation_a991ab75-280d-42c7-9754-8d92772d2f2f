package com.demo.mcp.model.request;

import lombok.Data;

import java.util.List;

@Data
public class LocationSearchRequest {
    
    /**
     * Text search query for location name or description
     */
    private String query;
    
    /**
     * Filter by category IDs
     */
    private List<String> category;
    
    /**
     * Filter by tags
     */
    private List<String> tags;
    
    /**
     * Latitude for geospatial search
     */
    private Double lat;
    
    /**
     * Longitude for geospatial search
     */
    private Double lng;
    
    /**
     * Search radius in meters for geospatial search
     */
    private Double radius;
    
    /**
     * Site ID to filter results (optional)
     */
    private String siteId;
    
    /**
     * Check if geospatial search parameters are provided
     * @return true if lat, lng, and radius are all provided
     */
    public boolean hasGeospatialParams() {
        return lat != null && lng != null && radius != null;
    }
    
    /**
     * Check if any search criteria is provided
     * @return true if any search parameter is provided
     */
    public boolean hasSearchCriteria() {
        return (query != null && !query.trim().isEmpty()) ||
               (category != null && !category.isEmpty()) ||
               (tags != null && !tags.isEmpty()) ||
               hasGeospatialParams();
    }
}
