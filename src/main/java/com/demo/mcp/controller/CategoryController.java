package com.demo.mcp.controller;

import com.demo.mcp.model.response.CategoryDto;
import com.demo.mcp.service.CategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/categories")
@RequiredArgsConstructor
public class CategoryController {
    
    private final CategoryService categoryService;
    
    /**
     * Retrieve all categories
     * @param siteId optional site ID to filter categories
     * @return List of categories
     */
    @GetMapping
    public ResponseEntity<List<CategoryDto>> getAllCategories(
            @RequestParam(value = "siteId", required = false) String siteId) {
        
        log.info("GET /categories - siteId: {}", siteId);
        
        try {
            List<CategoryDto> categories;
            
            if (siteId != null && !siteId.trim().isEmpty()) {
                categories = categoryService.getCategoriesBySiteId(siteId);
                log.debug("Retrieved {} categories for siteId: {}", categories.size(), siteId);
            } else {
                categories = categoryService.getAllCategories();
                log.debug("Retrieved {} categories", categories.size());
            }
            
            return ResponseEntity.ok(categories);
            
        } catch (Exception e) {
            log.error("Error retrieving categories", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Retrieve a category by ID
     * @param categoryId the category ID
     * @return Category if found, 404 if not found
     */
    @GetMapping("/{categoryId}")
    public ResponseEntity<CategoryDto> getCategoryById(@PathVariable String categoryId) {
        
        log.info("GET /categories/{}", categoryId);
        
        if (categoryId == null || categoryId.trim().isEmpty()) {
            log.warn("Category ID is null or empty");
            return ResponseEntity.badRequest().build();
        }
        
        try {
            Optional<CategoryDto> category = categoryService.getCategoryById(categoryId);
            
            if (category.isPresent()) {
                log.debug("Found category with id: {}", categoryId);
                return ResponseEntity.ok(category.get());
            } else {
                log.debug("Category not found with id: {}", categoryId);
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("Error retrieving category with id: {}", categoryId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Search categories by name
     * @param name the name to search for
     * @return List of matching categories
     */
    @GetMapping("/search")
    public ResponseEntity<List<CategoryDto>> searchCategories(
            @RequestParam("name") String name) {
        
        log.info("GET /categories/search - name: {}", name);
        
        if (name == null || name.trim().isEmpty()) {
            log.warn("Search name is null or empty");
            return ResponseEntity.badRequest().build();
        }
        
        try {
            List<CategoryDto> categories = categoryService.searchCategoriesByName(name);
            log.debug("Found {} categories matching name: {}", categories.size(), name);
            return ResponseEntity.ok(categories);
            
        } catch (Exception e) {
            log.error("Error searching categories with name: {}", name, e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
