package com.demo.mcp.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.demo.mcp.model.response.CategoryDto;
import com.demo.mcp.service.CategoryService;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/categories")
@RequiredArgsConstructor
public class CategoryController {

    private final CategoryService categoryService;

    @GetMapping
    public ResponseEntity<List<CategoryDto>> getAllCategories() {
        List<CategoryDto> categories = categoryService.getAllCategories();
        return ResponseEntity.ok(categories);
    }

    @GetMapping("/{siteId}")
    public ResponseEntity<List<CategoryDto>> getCategoriesBySiteId(@PathVariable String siteId) {
        List<CategoryDto> categories = categoryService.getCategoriesBySiteId(siteId);
        return ResponseEntity.ok(categories);
    }

    @GetMapping("/{siteId}/{categoryId}")
    public ResponseEntity<CategoryDto> getCategoryById(@PathVariable String siteId, @PathVariable String categoryId) {
        Optional<CategoryDto> category = categoryService.getCategoryById(categoryId);
        return category.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/search")
    public ResponseEntity<List<CategoryDto>> searchCategories(@RequestParam("name") String name) {
        List<CategoryDto> categories = categoryService.searchCategoriesByName(name);
        return ResponseEntity.ok(categories);
    }
}
