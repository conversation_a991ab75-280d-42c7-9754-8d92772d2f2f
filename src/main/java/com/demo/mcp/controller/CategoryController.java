package com.demo.mcp.controller;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.demo.mcp.mapper.CategoryMapper;
import com.demo.mcp.model.document.Category;
import com.demo.mcp.model.response.CategoryDto;
import com.demo.mcp.service.CategoryService;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/categories")
@RequiredArgsConstructor
public class CategoryController {

    private final CategoryService categoryService;
    private final CategoryMapper categoryMapper;

    @GetMapping
    public ResponseEntity<List<CategoryDto>> getAllCategories() {
        List<Category> categories = categoryService.getAllCategories();
        List<CategoryDto> categoryDtos = categories.stream()
                .map(categoryMapper::toCategoryDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(categoryDtos);
    }

    @GetMapping("/{siteId}")
    public ResponseEntity<List<CategoryDto>> getCategoriesBySiteId(@PathVariable String siteId) {
        List<Category> categories = categoryService.getCategoriesBySiteId(siteId);
        List<CategoryDto> categoryDtos = categories.stream()
                .map(categoryMapper::toCategoryDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(categoryDtos);
    }

    @GetMapping("/{siteId}/{categoryId}")
    public ResponseEntity<CategoryDto> getCategoryById(@PathVariable String siteId, @PathVariable String categoryId) {
        Optional<Category> category = categoryService.getCategoryById(categoryId);
        return category.map(categoryMapper::toCategoryDto)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/search")
    public ResponseEntity<List<CategoryDto>> searchCategories(@RequestParam("name") String name) {
        List<Category> categories = categoryService.searchCategoriesByName(name);
        List<CategoryDto> categoryDtos = categories.stream()
                .map(categoryMapper::toCategoryDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(categoryDtos);
    }
}
