package com.demo.mcp.controller;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.demo.mcp.model.document.Location;
import com.demo.mcp.service.ToolService;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/locations")
@RequiredArgsConstructor
public class ToolController {

    private final ToolService toolService;

    /**
     * Location Search API
     *
     * @param siteId Site ID to search within (required)
     * @param query Text search for location name (optional)
     * @param category Filter by category (optional, array)
     * @param tags Filter by tags (optional, array)
     * @return Array of POI objects or error response
     */
    @GetMapping("/search")
    public ResponseEntity<?> searchLocations(
            @RequestParam(value = "siteId") String siteId,
            @RequestParam(value = "query", required = false) String query,
            @RequestParam(value = "category", required = false) List<String> category,
            @RequestParam(value = "tags", required = false) List<String> tags) {

        return toolService.searchLocations(siteId, query, category, tags);
    }
}
