package com.demo.mcp.controller;

import com.demo.mcp.model.request.LocationSearchRequest;
import com.demo.mcp.model.response.LocationDto;
import com.demo.mcp.service.ToolService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/tools")
@RequiredArgsConstructor
public class ToolController {
    
    private final ToolService toolService;
    
    /**
     * Advanced location search with multiple criteria including geospatial search
     * 
     * @param query Text search (optional)
     * @param category Filter by category (optional, array)
     * @param tags Filter by tags (optional, array)
     * @param lat Latitude for geospatial search (optional)
     * @param lng Longitude for geospatial search (optional)
     * @param radius Search radius in meters (optional)
     * @param siteId Filter by site ID (optional)
     * @return List of matching locations
     */
    @GetMapping("/locations/search")
    public ResponseEntity<List<LocationDto>> searchLocations(
            @RequestParam(value = "query", required = false) String query,
            @RequestParam(value = "category", required = false) List<String> category,
            @RequestParam(value = "tags", required = false) List<String> tags,
            @RequestParam(value = "lat", required = false) Double lat,
            @RequestParam(value = "lng", required = false) Double lng,
            @RequestParam(value = "radius", required = false) Double radius,
            @RequestParam(value = "siteId", required = false) String siteId) {
        
        log.info("GET /tools/locations/search - query: {}, category: {}, tags: {}, lat: {}, lng: {}, radius: {}, siteId: {}", 
                query, category, tags, lat, lng, radius, siteId);
        
        try {
            // Validate geospatial parameters
            if (isPartialGeospatialParams(lat, lng, radius)) {
                log.warn("Incomplete geospatial parameters provided. All of lat, lng, and radius must be provided together.");
                return ResponseEntity.badRequest().build();
            }
            
            // Validate radius if provided
            if (radius != null && radius <= 0) {
                log.warn("Invalid radius value: {}. Radius must be positive.", radius);
                return ResponseEntity.badRequest().build();
            }
            
            // Create search request
            LocationSearchRequest searchRequest = new LocationSearchRequest();
            searchRequest.setQuery(query);
            searchRequest.setCategory(category);
            searchRequest.setTags(tags);
            searchRequest.setLat(lat);
            searchRequest.setLng(lng);
            searchRequest.setRadius(radius);
            searchRequest.setSiteId(siteId);
            
            // Perform search
            List<LocationDto> locations = toolService.searchLocations(searchRequest);
            
            log.debug("Advanced search returned {} locations", locations.size());
            return ResponseEntity.ok(locations);
            
        } catch (Exception e) {
            log.error("Error performing advanced location search", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Check if geospatial parameters are partially provided
     * @param lat latitude
     * @param lng longitude
     * @param radius radius
     * @return true if some but not all geospatial parameters are provided
     */
    private boolean isPartialGeospatialParams(Double lat, Double lng, Double radius) {
        int providedParams = 0;
        if (lat != null) providedParams++;
        if (lng != null) providedParams++;
        if (radius != null) providedParams++;
        
        return providedParams > 0 && providedParams < 3;
    }
}
