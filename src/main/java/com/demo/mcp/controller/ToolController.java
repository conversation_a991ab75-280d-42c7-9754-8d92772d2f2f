package com.demo.mcp.controller;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.demo.mcp.model.document.Location;
import com.demo.mcp.service.ToolService;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/locations")
@RequiredArgsConstructor
public class ToolController {

    private final ToolService toolService;

    /**
     * Location Search API
     * 
     * @param query Text search (optional)
     * @param category Filter by category (optional, array)
     * @param tags Filter by tags (optional, array)
     * @param lat Latitude for geospatial search (optional)
     * @param lng Longitude for geospatial search (optional)
     * @param radius Search radius in meters (optional)
     * @return Array of POI objects
     */
    @GetMapping("/search")
    public ResponseEntity<List<Location>> searchLocations(
            @RequestParam(value = "query", required = false) String query,
            @RequestParam(value = "category", required = false) List<String> category,
            @RequestParam(value = "tags", required = false) List<String> tags,
            @RequestParam(value = "lat", required = false) Double lat,
            @RequestParam(value = "lng", required = false) Double lng,
            @RequestParam(value = "radius", required = false) Double radius) {

        List<Location> locations = toolService.searchLocations(query, category, tags, lat, lng, radius);
        return ResponseEntity.ok(locations);
    }
}
