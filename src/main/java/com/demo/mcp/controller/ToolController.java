package com.demo.mcp.controller;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.demo.mcp.mapper.LocationMapper;
import com.demo.mcp.model.document.Location;
import com.demo.mcp.model.request.LocationSearchRequest;
import com.demo.mcp.model.response.LocationDto;
import com.demo.mcp.service.ToolService;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/tools")
@RequiredArgsConstructor
public class ToolController {

    private final ToolService toolService;
    private final LocationMapper locationMapper;

    @GetMapping("/locations/search")
    public ResponseEntity<List<LocationDto>> searchLocations(
            @RequestParam(value = "query", required = false) String query,
            @RequestParam(value = "category", required = false) List<String> category,
            @RequestParam(value = "tags", required = false) List<String> tags,
            @RequestParam(value = "lat", required = false) Double lat,
            @RequestParam(value = "lng", required = false) Double lng,
            @RequestParam(value = "radius", required = false) Double radius,
            @RequestParam(value = "siteId", required = false) String siteId) {

        LocationSearchRequest searchRequest = new LocationSearchRequest();
        searchRequest.setQuery(query);
        searchRequest.setCategory(category);
        searchRequest.setTags(tags);
        searchRequest.setLat(lat);
        searchRequest.setLng(lng);
        searchRequest.setRadius(radius);
        searchRequest.setSiteId(siteId);

        List<Location> locations = toolService.searchLocations(searchRequest);
        List<LocationDto> locationDtos = locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(locationDtos);
    }

    /**
     * MCP-style location search endpoint that returns structured response
     */
    @GetMapping("/locations/mcp-search")
    public ResponseEntity<Object> mcpLocationSearch(
            @RequestParam(value = "query", required = false) String query,
            @RequestParam(value = "category", required = false) List<String> category,
            @RequestParam(value = "tags", required = false) List<String> tags,
            @RequestParam(value = "lat", required = false) Double lat,
            @RequestParam(value = "lng", required = false) Double lng,
            @RequestParam(value = "radius", required = false) Double radius) {

        Object result = toolService.becoLocationSearch(query, category, tags, lat, lng, radius);
        return ResponseEntity.ok(result);
    }
}
