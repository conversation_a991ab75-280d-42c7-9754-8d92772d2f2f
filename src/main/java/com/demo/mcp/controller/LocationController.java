package com.demo.mcp.controller;

import com.demo.mcp.model.response.LocationDto;
import com.demo.mcp.service.LocationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/locations")
@RequiredArgsConstructor
public class LocationController {
    
    private final LocationService locationService;
    
    /**
     * Retrieve all locations
     * @param siteId optional site ID to filter locations
     * @return List of locations
     */
    @GetMapping
    public ResponseEntity<List<LocationDto>> getAllLocations(
            @RequestParam(value = "siteId", required = false) String siteId) {
        
        log.info("GET /locations - siteId: {}", siteId);
        
        try {
            List<LocationDto> locations;
            
            if (siteId != null && !siteId.trim().isEmpty()) {
                locations = locationService.getLocationsBySiteId(siteId);
                log.debug("Retrieved {} locations for siteId: {}", locations.size(), siteId);
            } else {
                locations = locationService.getAllLocations();
                log.debug("Retrieved {} locations", locations.size());
            }
            
            return ResponseEntity.ok(locations);
            
        } catch (Exception e) {
            log.error("Error retrieving locations", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Retrieve a location by ID
     * @param locationId the location ID
     * @return Location if found, 404 if not found
     */
    @GetMapping("/{locationId}")
    public ResponseEntity<LocationDto> getLocationById(@PathVariable String locationId) {
        
        log.info("GET /locations/{}", locationId);
        
        if (locationId == null || locationId.trim().isEmpty()) {
            log.warn("Location ID is null or empty");
            return ResponseEntity.badRequest().build();
        }
        
        try {
            Optional<LocationDto> location = locationService.getLocationById(locationId);
            
            if (location.isPresent()) {
                log.debug("Found location with id: {}", locationId);
                return ResponseEntity.ok(location.get());
            } else {
                log.debug("Location not found with id: {}", locationId);
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("Error retrieving location with id: {}", locationId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Search locations by name
     * @param name the name to search for
     * @return List of matching locations
     */
    @GetMapping("/search")
    public ResponseEntity<List<LocationDto>> searchLocations(
            @RequestParam("name") String name) {
        
        log.info("GET /locations/search - name: {}", name);
        
        if (name == null || name.trim().isEmpty()) {
            log.warn("Search name is null or empty");
            return ResponseEntity.badRequest().build();
        }
        
        try {
            List<LocationDto> locations = locationService.searchLocationsByName(name);
            log.debug("Found {} locations matching name: {}", locations.size(), name);
            return ResponseEntity.ok(locations);
            
        } catch (Exception e) {
            log.error("Error searching locations with name: {}", name, e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
