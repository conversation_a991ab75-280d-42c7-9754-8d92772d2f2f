package com.demo.mcp.controller;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.demo.mcp.mapper.LocationMapper;
import com.demo.mcp.model.document.Location;
import com.demo.mcp.model.response.LocationDto;
import com.demo.mcp.service.LocationService;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/locations")
@RequiredArgsConstructor
public class LocationController {

    private final LocationService locationService;
    private final LocationMapper locationMapper;

    @GetMapping
    public ResponseEntity<List<LocationDto>> getAllLocations() {
        List<Location> locations = locationService.getAllLocations();
        List<LocationDto> locationDtos = locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(locationDtos);
    }

    @GetMapping("/{siteId}")
    public ResponseEntity<List<LocationDto>> getLocationsBySiteId(@PathVariable String siteId) {
        List<Location> locations = locationService.getLocationsBySiteId(siteId);
        List<LocationDto> locationDtos = locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(locationDtos);
    }

    @GetMapping("/{siteId}/{locationId}")
    public ResponseEntity<LocationDto> getLocationById(@PathVariable String siteId, @PathVariable String locationId) {
        Optional<Location> location = locationService.getLocationById(locationId);
        return location.map(locationMapper::toLocationDto)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/search")
    public ResponseEntity<List<LocationDto>> searchLocations(@RequestParam("name") String name) {
        List<Location> locations = locationService.searchLocationsByName(name);
        List<LocationDto> locationDtos = locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(locationDtos);
    }
}
