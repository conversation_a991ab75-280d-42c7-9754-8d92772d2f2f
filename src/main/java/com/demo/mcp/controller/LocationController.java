package com.demo.mcp.controller;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.demo.mcp.mapper.LocationMapper;
import com.demo.mcp.model.document.Location;
import com.demo.mcp.model.response.LocationDto;
import com.demo.mcp.model.response.McpErrorResponse;
import com.demo.mcp.service.LocationService;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/locations")
@RequiredArgsConstructor
public class LocationController {

    private final LocationService locationService;
    private final LocationMapper locationMapper;

    @GetMapping
    public ResponseEntity<List<LocationDto>> getAllLocations() {
        List<Location> locations = locationService.getAllLocations();
        List<LocationDto> locationDtos = locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(locationDtos);
    }

    @GetMapping("/{siteId}")
    public ResponseEntity<List<LocationDto>> getLocationsBySiteId(@PathVariable String siteId) {
        List<Location> locations = locationService.getLocationsBySiteId(siteId);
        List<LocationDto> locationDtos = locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(locationDtos);
    }

    @GetMapping("/{siteId}/{locationId}")
    public ResponseEntity<LocationDto> getLocationById(@PathVariable String siteId, @PathVariable String locationId) {
        Optional<Location> location = locationService.getLocationById(locationId);
        return location.map(locationMapper::toLocationDto)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/search")
    public ResponseEntity<?> searchLocations(
            @RequestParam(value = "query", required = false) String query,
            @RequestParam(value = "category", required = false) List<String> category,
            @RequestParam(value = "tags", required = false) List<String> tags,
            @RequestParam(value = "lat", required = false) Double lat,
            @RequestParam(value = "lng", required = false) Double lng,
            @RequestParam(value = "radius", required = false) Double radius) {

        // Validate geospatial parameters
        if (isPartialGeospatialParams(lat, lng, radius)) {
            return ResponseEntity.status(422).body(
                McpErrorResponse.validationError(422,
                    "Incomplete geospatial parameters. All of lat, lng, and radius must be provided together.",
                    Map.of("lat", lat != null ? lat : "null",
                           "lng", lng != null ? lng : "null",
                           "radius", radius != null ? radius : "null")));
        }

        // Validate latitude range
        if (lat != null && (lat < -90 || lat > 90)) {
            return ResponseEntity.status(422).body(
                McpErrorResponse.validationError(422,
                    "Invalid coordinate values - latitude must be between -90 and 90",
                    Map.of("lat", lat, "lng", lng != null ? lng : "null")));
        }

        // Validate longitude range
        if (lng != null && (lng < -180 || lng > 180)) {
            return ResponseEntity.status(422).body(
                McpErrorResponse.validationError(422,
                    "Invalid coordinate values - longitude must be between -180 and 180",
                    Map.of("lat", lat != null ? lat : "null", "lng", lng)));
        }

        // Validate radius if provided
        if (radius != null && radius <= 0) {
            return ResponseEntity.status(422).body(
                McpErrorResponse.validationError(422,
                    "Invalid radius value. Radius must be positive.",
                    Map.of("radius", radius)));
        }

        try {
            List<Location> locations = locationService.searchLocations(query, category, tags, lat, lng, radius);
            List<LocationDto> locationDtos = locations.stream()
                    .map(locationMapper::toLocationDto)
                    .collect(Collectors.toList());
            return ResponseEntity.ok(locationDtos);
        } catch (Exception e) {
            return ResponseEntity.status(500).body(
                McpErrorResponse.error(500, "An error occurred while searching locations: " + e.getMessage()));
        }
    }

    /**
     * Check if geospatial parameters are partially provided
     */
    private boolean isPartialGeospatialParams(Double lat, Double lng, Double radius) {
        int providedParams = 0;
        if (lat != null) providedParams++;
        if (lng != null) providedParams++;
        if (radius != null) providedParams++;

        return providedParams > 0 && providedParams < 3;
    }
}
