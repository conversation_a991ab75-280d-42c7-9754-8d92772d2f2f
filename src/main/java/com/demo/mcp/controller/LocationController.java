package com.demo.mcp.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.demo.mcp.model.response.LocationDto;
import com.demo.mcp.service.LocationService;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/locations")
@RequiredArgsConstructor
public class LocationController {

    private final LocationService locationService;

    @GetMapping
    public ResponseEntity<List<LocationDto>> getAllLocations() {
        List<LocationDto> locations = locationService.getAllLocations();
        return ResponseEntity.ok(locations);
    }

    @GetMapping("/{siteId}")
    public ResponseEntity<List<LocationDto>> getLocationsBySiteId(@PathVariable String siteId) {
        List<LocationDto> locations = locationService.getLocationsBySiteId(siteId);
        return ResponseEntity.ok(locations);
    }

    @GetMapping("/{siteId}/{locationId}")
    public ResponseEntity<LocationDto> getLocationById(@PathVariable String siteId, @PathVariable String locationId) {
        Optional<LocationDto> location = locationService.getLocationById(locationId);
        return location.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/search")
    public ResponseEntity<List<LocationDto>> searchLocations(@RequestParam("name") String name) {
        List<LocationDto> locations = locationService.searchLocationsByName(name);
        return ResponseEntity.ok(locations);
    }
}
