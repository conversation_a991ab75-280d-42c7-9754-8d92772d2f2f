package com.demo.mcp.mapper;


import com.demo.mcp.model.document.Node;
import com.demo.mcp.model.response.NodeDto;
import org.geojson.LngLatAlt;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.time.Instant;
import java.util.List;

import static org.mapstruct.NullValueMappingStrategy.RETURN_NULL;
import static org.mapstruct.ReportingPolicy.IGNORE;

@Mapper(componentModel = "spring", unmappedTargetPolicy = IGNORE, nullValueMappingStrategy = RETURN_NULL)
public interface NodeMapper {

    @Mapping(target = "siblings", ignore = true) // Ignore siblings field for now
    @Mapping(target = "accessible", constant = "true") // Set accessible to true by default
    @Mapping(target = "lat", source = "latitude")
    @Mapping(target = "lng", source = "longitude")
    Node toNode(LngLatAlt lngLatAlt);

    @AfterMapping
    default void initializeTimestamps(@MappingTarget Node node) {
        Instant now = Instant.now();
        node.setDateCreated(now);
        node.setLastUpdated(now);
    }

    @Mapping(source = "floorId", target = "floorId")
    @Mapping(source = "siteId", target = "siteId")
    NodeDto toNodeDto(Node node);

    List<NodeDto> toNodeDtoList(List<Node> nodes);

}
