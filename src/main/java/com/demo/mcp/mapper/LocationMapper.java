package com.demo.mcp.mapper;

import com.demo.mcp.model.document.Location;
import com.demo.mcp.model.response.LocationDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import static org.mapstruct.NullValueMappingStrategy.RETURN_NULL;
import static org.mapstruct.ReportingPolicy.IGNORE;

@Mapper(componentModel = "spring", unmappedTargetPolicy = IGNORE, nullValueMappingStrategy = RETURN_NULL)
public abstract class LocationMapper {

//    @Autowired
//    private ImageService imageService;

    @Mapping(source = "node.nodeId", target = "nodeId")
    public abstract LocationDto toLocationDto(Location location);


    @Mapping(target = "locationId", source = "locationId")
    @Mapping(target = "siteId", source = "siteId")
    public abstract Location fromLocationParams(String siteId, String locationId);


//    @AfterMapping
//    protected void mapImageUrl(Location source, @MappingTarget LocationDto dto) {
//        if (source.getLogo() != null) {
//            dto.setLogoUrl(imageService.getImageUrl(source.getLogo()));
//        }
//
//        if (source.getBanner() != null) {
//            dto.setBannerUrl(imageService.getImageUrl(source.getBanner()));
//        }
//
//        if (dto.getMapObject() != null && dto
//                .getMapObject()
//                .getModelPath() != null) {
//            dto
//                    .getMapObject()
//                    .setModelPath(imageService.getImageUrl(dto
//                            .getMapObject()
//                            .getModelPath()));
//        }
//    }

    @Mapping(source = "node.nodeId", target = "nodeId")
    public abstract LocationDto toSdkLocationDto(Location location);

//    @AfterMapping
//    protected void mapSdkImageUrl(Location source, @MappingTarget SdkLocationDto dto) {
//        if (source.getLogo() != null) {
//            dto.setLogoUrl(imageService.getImageUrl(source.getLogo()));
//        }
//
//        if (source.getBanner() != null) {
//            dto.setBannerUrl(imageService.getImageUrl(source.getBanner()));
//        }
//    }

}
