package com.demo.mcp.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import static org.mapstruct.NullValueMappingStrategy.RETURN_NULL;
import static org.mapstruct.ReportingPolicy.IGNORE;

import com.demo.mcp.model.document.Location;
import com.demo.mcp.model.response.LocationDto;

@Mapper(componentModel = "spring", unmappedTargetPolicy = IGNORE, nullValueMappingStrategy = RETURN_NULL)
public interface LocationMapper {

    @Mapping(source = "node.nodeId", target = "nodeId")
    LocationDto toLocationDto(Location location);

    @Mapping(target = "locationId", source = "locationId")
    @Mapping(target = "siteId", source = "siteId")
    Location fromLocationParams(String siteId, String locationId);

    @Mapping(source = "node.nodeId", target = "nodeId")
    LocationDto toSdkLocationDto(Location location);
}
