package com.demo.mcp.mapper;


import com.demo.mcp.model.document.Category;
import com.demo.mcp.model.response.CategoryDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import static org.mapstruct.NullValueMappingStrategy.RETURN_NULL;
import static org.mapstruct.ReportingPolicy.IGNORE;

@Mapper(componentModel = "spring", unmappedTargetPolicy = IGNORE, nullValueMappingStrategy = RETURN_NULL)
public abstract class CategoryMapper {

//    @Autowired
//    private ImageService imageService;

    public abstract CategoryDto toCategoryDto(Category category);


    @Mapping(target = "categoryId", source = "categoryId")
    @Mapping(target = "siteId", source = "siteId")
    public abstract Category fromCategoryParams(String categoryId, String siteId);


}
