package com.demo.mcp.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import static org.mapstruct.NullValueMappingStrategy.RETURN_NULL;
import static org.mapstruct.ReportingPolicy.IGNORE;

import com.demo.mcp.model.document.Category;
import com.demo.mcp.model.response.CategoryDto;

@Mapper(componentModel = "spring", unmappedTargetPolicy = IGNORE, nullValueMappingStrategy = RETURN_NULL)
public interface CategoryMapper {

    CategoryDto toCategoryDto(Category category);

    @Mapping(target = "categoryId", source = "categoryId")
    @Mapping(target = "siteId", source = "siteId")
    Category fromCategoryParams(String categoryId, String siteId);
}
