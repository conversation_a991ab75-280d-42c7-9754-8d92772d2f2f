package com.demo.mcp.service;

import java.util.List;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Service;

import com.demo.mcp.model.document.Location;
import com.demo.mcp.repository.LocationRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ToolService {

    private final LocationRepository locationRepository;

    /**
     * Search locations by name, category, tags, or geospatial coordinates
     *
     * @param query Text search for location names (optional)
     * @param category Filter by category IDs (optional, array)
     * @param tags Filter by tags (optional, array)
     * @param lat Latitude for geospatial search (optional)
     * @param lng Longitude for geospatial search (optional)
     * @param radius Search radius in meters for geospatial search (optional)
     * @return List of matching locations
     */
    @Tool(name = "beco_location_search", description = "Search locations by name, category, tags, or geospatial coordinates")
    public List<Location> searchLocations(
            String query,
            List<String> category,
            List<String> tags,
            Double lat,
            Double lng,
            Double radius) {

        log.info("MCP Tool beco_location_search called with parameters: query={}, category={}, tags={}, lat={}, lng={}, radius={}",
                query, category, tags, lat, lng, radius);

        // Use MongoRepository methods directly for search
        if (query != null && !query.trim().isEmpty() &&
            category != null && !category.isEmpty() &&
            tags != null && !tags.isEmpty()) {
            return locationRepository.findBySearchCriteria(query.trim(), category, tags);
        } else if (query != null && !query.trim().isEmpty()) {
            return locationRepository.findByNameContainingIgnoreCase(query.trim());
        } else if (category != null && !category.isEmpty()) {
            return locationRepository.findByCategoriesIn(category);
        } else if (tags != null && !tags.isEmpty()) {
            return locationRepository.findByTagsIn(tags);
        } else {
            return locationRepository.findAll();
        }
    }


}
