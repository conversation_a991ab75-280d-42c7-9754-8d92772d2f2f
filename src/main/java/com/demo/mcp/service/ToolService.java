package com.demo.mcp.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.demo.mcp.model.document.Location;
import com.demo.mcp.model.request.LocationSearchRequest;
import com.demo.mcp.repository.LocationRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ToolService {

    private final LocationRepository locationRepository;

    public List<Location> searchLocations(LocationSearchRequest searchRequest) {
        if (!searchRequest.hasSearchCriteria()) {
            return getAllLocations();
        }

        List<Location> locations;

        if (searchRequest.getQuery() != null && !searchRequest.getQuery().trim().isEmpty() &&
            searchRequest.getCategory() != null && !searchRequest.getCategory().isEmpty() &&
            searchRequest.getTags() != null && !searchRequest.getTags().isEmpty()) {
            locations = locationRepository.findBySearchCriteria(
                searchRequest.getQuery().trim(),
                searchRequest.getCategory(),
                searchRequest.getTags()
            );
        } else if (searchRequest.getQuery() != null && !searchRequest.getQuery().trim().isEmpty()) {
            locations = locationRepository.findByNameContainingIgnoreCase(searchRequest.getQuery().trim());
        } else if (searchRequest.getCategory() != null && !searchRequest.getCategory().isEmpty()) {
            locations = locationRepository.findByCategoriesIn(searchRequest.getCategory());
        } else if (searchRequest.getTags() != null && !searchRequest.getTags().isEmpty()) {
            locations = locationRepository.findByTagsIn(searchRequest.getTags());
        } else {
            locations = locationRepository.findAll();
        }

        if (searchRequest.getSiteId() != null && !searchRequest.getSiteId().trim().isEmpty()) {
            locations = locations.stream()
                    .filter(location -> searchRequest.getSiteId().equals(location.getSiteId()))
                    .collect(Collectors.toList());
        }

        return locations;
    }

    private List<Location> getAllLocations() {
        return locationRepository.findAll();
    }
}
