package com.demo.mcp.service;

import java.util.List;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Service;

import com.demo.mcp.model.document.Location;
import com.demo.mcp.repository.LocationRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ToolService {

    private final LocationRepository locationRepository;

    /**
     * MCP Tool for searching locations with various criteria within a site
     * This tool exposes the location search functionality to external MCP clients
     *
     * @param siteId   Site ID to search within (required)
     * @param query    Text search for location names (optional)
     * @param category Filter by category IDs (optional, array)
     * @param tags     Filter by tags (optional, array)
     * @param lat      Latitude for geospatial search (optional)
     * @param lng      Longitude for geospatial search (optional)
     * @param radius   Search radius in meters for geospatial search (optional)
     * @return McpLocationSearchResponse containing search results and metadata
     */
    @Tool(name = "beco_location_search", description = "Search locations by name, category, tags, or geospatial coordinates")
    public List<Location> searchLocations(String query, List<String> category, List<String> tags, Double lat, Double lng, Double radius) {

        log.info("Location search called with parameters: query={}, category={}, tags={}, lat={}, lng={}, radius={}",
                query, category, tags, lat, lng, radius);

        // Use optimized MongoDB queries based on different parameters
        // Since REST API doesn't require siteId, search across all sites
        return performLocationSearch(query, category, tags, lat, lng, radius);
    }

    /**
     * Perform location search across all sites using MongoRepository methods directly
     */
    private List<Location> performLocationSearch(String query, List<String> category, List<String> tags,
                                                Double lat, Double lng, Double radius) {

        // For geospatial search, we would need to implement geospatial queries
        // For now, we'll focus on text, category, and tag searches
        if (hasGeospatialParams(lat, lng, radius)) {
            // TODO: Implement geospatial search using MongoDB geospatial queries
            log.warn("Geospatial search not yet implemented, falling back to text/category/tag search");
        }

        // Use optimized MongoRepository methods for different search scenarios across all sites
        if (query != null && !query.trim().isEmpty()) {
            return locationRepository.findByNameContainingIgnoreCase(query.trim());
        } else if (category != null && !category.isEmpty()) {
            return locationRepository.findByCategoriesIn(category);
        } else if (tags != null && !tags.isEmpty()) {
            return locationRepository.findByTagsIn(tags);
        } else {
            return locationRepository.findAll();
        }
    }



    /**
     * Check if all geospatial parameters are provided
     */
    private boolean hasGeospatialParams(Double lat, Double lng, Double radius) {
        return lat != null && lng != null && radius != null;
    }
}
