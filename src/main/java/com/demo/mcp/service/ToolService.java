package com.demo.mcp.service;

import java.util.List;
import java.util.Map;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.demo.mcp.model.document.Location;
import com.demo.mcp.model.response.McpErrorResponse;
import com.demo.mcp.repository.LocationRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ToolService {

    private final LocationRepository locationRepository;

    /**
     * MCP Tool for searching locations with various criteria within a site
     * This tool exposes the location search functionality to external MCP clients
     *
     * @param siteId   Site ID to search within (required)
     * @param query    Text search for location names (optional)
     * @param category Filter by category IDs (optional, array)
     * @param tags     Filter by tags (optional, array)
     * @return McpLocationSearchResponse containing search results and metadata
     */
    @Tool(name = "beco_location_search", description = "Search locations by name, category, and tags within a site")
    public ResponseEntity<?> searchLocations(String siteId, String query, List<String> category, List<String> tags) {

        log.info("Location search called with parameters: siteId={}, query={}, category={}, tags={}",
                siteId, query, category, tags);

        // Validate required siteId
        if (siteId == null || siteId.trim().isEmpty()) {
            log.warn("SiteId is required but not provided");
            return ResponseEntity.status(422).body(McpErrorResponse.validationError(422,
                    "SiteId is required for location search",
                    Map.of("siteId", "null or empty")));
        }

        // Check if siteId exists in database
        if (!locationRepository.existsBySiteId(siteId)) {
            log.warn("Invalid siteId provided: {}", siteId);
            return ResponseEntity.status(422).body(McpErrorResponse.validationError(422,
                    "Invalid siteId - site does not exist",
                    Map.of("siteId", siteId)));
        }

        try {
            // Use optimized MongoDB queries based on different parameters
            List<Location> locations = performSiteBasedLocationSearch(siteId, query, category, tags);
            log.info("Location search completed successfully. Found {} locations for siteId: {}", locations.size(), siteId);
            return ResponseEntity.ok(locations);
        } catch (Exception e) {
            log.error("Error performing location search for siteId: {}", siteId, e);
            return ResponseEntity.status(500).body(McpErrorResponse.error(500,
                    "An error occurred while searching locations: " + e.getMessage()));
        }
    }

    /**
     * Perform site-based location search using MongoRepository methods directly
     */
    private List<Location> performSiteBasedLocationSearch(String siteId, String query, List<String> category, List<String> tags) {

        // Use optimized site-based MongoRepository methods for different search scenarios
        if (query != null && !query.trim().isEmpty()) {
            return locationRepository.findBySiteIdAndNameContainingIgnoreCase(siteId, query.trim());
        } else if (category != null && !category.isEmpty()) {
            return locationRepository.findBySiteIdAndCategoriesIn(siteId, category);
        } else if (tags != null && !tags.isEmpty()) {
            return locationRepository.findBySiteIdAndTagsIn(siteId, tags);
        } else {
            return locationRepository.findBySiteId(siteId);
        }
    }
}
