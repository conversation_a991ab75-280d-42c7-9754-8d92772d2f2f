package com.demo.mcp.service;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.demo.mcp.model.document.Location;
import com.demo.mcp.model.response.McpErrorResponse;
import com.demo.mcp.model.response.McpLocationSearchResponse;
import com.demo.mcp.repository.LocationRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ToolService {

    private final LocationRepository locationRepository;

    /**
     * MCP Tool for searching locations with various criteria
     * This tool exposes the location search functionality to external MCP clients
     *
     * @param query    Text search for location names (optional)
     * @param category Filter by category IDs (optional, array)
     * @param tags     Filter by tags (optional, array)
     * @param lat      Latitude for geospatial search (optional)
     * @param lng      Longitude for geospatial search (optional)
     * @param radius   Search radius in meters for geospatial search (optional)
     * @return McpLocationSearchResponse containing search results and metadata
     */
    // @Tool(name = "beco_location_search", description = "Search locations by name, category, tags, or geospatial coordinates")
    public Object searchLocations(
            String query,
            List<String> category,
            List<String> tags,
            Double lat,
            Double lng,
            Double radius) {

        log.info("MCP Tool beco_location_search called with parameters: query={}, category={}, tags={}, lat={}, lng={}, radius={}",
                query, category, tags, lat, lng, radius);

        // Validate geospatial parameters
        if (isPartialGeospatialParams(lat, lng, radius)) {
            log.warn("Incomplete geospatial parameters provided. All of lat, lng, and radius must be provided together.");
            return McpErrorResponse.validationError(422,
                    "Incomplete geospatial parameters. All of lat, lng, and radius must be provided together.",
                    Map.of("lat", lat, "lng", lng, "radius", radius));
        }

        // Validate latitude range
        if (lat != null && (lat < -90 || lat > 90)) {
            log.warn("Invalid latitude value: {}. Latitude must be between -90 and 90", lat);
            return McpErrorResponse.validationError(422,
                    "Invalid coordinate values - latitude must be between -90 and 90",
                    Map.of("lat", lat, "lng", lng));
        }

        // Validate longitude range
        if (lng != null && (lng < -180 || lng > 180)) {
            log.warn("Invalid longitude value: {}. Longitude must be between -180 and 180", lng);
            return McpErrorResponse.validationError(422,
                    "Invalid coordinate values - longitude must be between -180 and 180",
                    Map.of("lat", lat, "lng", lng));
        }

        // Validate radius if provided
        if (radius != null && radius <= 0) {
            log.warn("Invalid radius value: {}. Radius must be positive.", radius);
            return McpErrorResponse.validationError(422,
                    "Invalid radius value. Radius must be positive.",
                    Map.of("radius", radius));
        }

        try {
            // Perform the search using MongoRepository methods directly
            List<Location> locations = performLocationSearch(query, category, tags, lat, lng, radius);

            log.info("MCP Tool search completed successfully. Found {} locations", locations.size());

            // Create search criteria for response
            McpLocationSearchResponse.SearchCriteria searchCriteria = new McpLocationSearchResponse.SearchCriteria(
                    query != null ? query : "",
                    category != null ? category : List.of(),
                    tags != null ? tags : List.of(),
                    hasGeospatialParams(lat, lng, radius),
                    lat,
                    lng,
                    radius
            );

            return McpLocationSearchResponse.success(locations, searchCriteria);

        } catch (Exception e) {
            log.error("Error performing MCP location search", e);
            return McpErrorResponse.error(500, "An error occurred while searching locations: " + e.getMessage());
        }
    }

    /**
     * Perform location search using MongoRepository methods directly
     */
    private List<Location> performLocationSearch(String query, List<String> category, List<String> tags,
                                                 Double lat, Double lng, Double radius) {

        // For geospatial search, we would need to implement geospatial queries
        // For now, we'll focus on text, category, and tag searches
        if (hasGeospatialParams(lat, lng, radius)) {
            // TODO: Implement geospatial search using MongoDB geospatial queries
            log.warn("Geospatial search not yet implemented, falling back to text/category/tag search");
        }

        // Use MongoRepository methods directly for search
        if (query != null && !query.trim().isEmpty() &&
                category != null && !category.isEmpty() &&
                tags != null && !tags.isEmpty()) {
            return locationRepository.findBySearchCriteria(query.trim(), category, tags);
        } else if (query != null && !query.trim().isEmpty()) {
            return locationRepository.findByNameContainingIgnoreCase(query.trim());
        } else if (category != null && !category.isEmpty()) {
            return locationRepository.findByCategoriesIn(category);
        } else if (tags != null && !tags.isEmpty()) {
            return locationRepository.findByTagsIn(tags);
        } else {
            return locationRepository.findAll();
        }
    }

    /**
     * Check if geospatial parameters are partially provided
     */
    private boolean isPartialGeospatialParams(Double lat, Double lng, Double radius) {
        int providedParams = 0;
        if (lat != null) providedParams++;
        if (lng != null) providedParams++;
        if (radius != null) providedParams++;

        return providedParams > 0 && providedParams < 3;
    }

    /**
     * Check if all geospatial parameters are provided
     */
    private boolean hasGeospatialParams(Double lat, Double lng, Double radius) {
        return lat != null && lng != null && radius != null;
    }
}
