package com.demo.mcp.service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import com.demo.mcp.mapper.LocationMapper;
import com.demo.mcp.model.document.Location;
import com.demo.mcp.model.request.LocationSearchRequest;
import com.demo.mcp.model.response.LocationDto;
import com.demo.mcp.repository.LocationRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ToolService {
    
    private final LocationRepository locationRepository;
    private final LocationMapper locationMapper;
    private final MongoTemplate mongoTemplate;
    
    /**
     * Advanced location search with multiple criteria including geospatial search
     * @param searchRequest the search parameters
     * @return List of matching LocationDto
     */
    public List<LocationDto> searchLocations(LocationSearchRequest searchRequest) {
        log.debug("Performing advanced location search with criteria: {}", searchRequest);
        
        if (!searchRequest.hasSearchCriteria()) {
            log.debug("No search criteria provided, returning all locations");
            return getAllLocations();
        }
        
        List<Criteria> criteriaList = new ArrayList<>();
        
        // Text search on name and description
        if (searchRequest.getQuery() != null && !searchRequest.getQuery().trim().isEmpty()) {
            String queryText = searchRequest.getQuery().trim();
            Criteria textCriteria = new Criteria().orOperator(
                Criteria.where("name").regex(queryText, "i"),
                Criteria.where("description").regex(queryText, "i")
            );
            criteriaList.add(textCriteria);
            log.debug("Added text search criteria for: {}", queryText);
        }
        
        // Filter by categories
        if (searchRequest.getCategory() != null && !searchRequest.getCategory().isEmpty()) {
            criteriaList.add(Criteria.where("categories").in(searchRequest.getCategory()));
            log.debug("Added category filter for: {}", searchRequest.getCategory());
        }
        
        // Filter by tags
        if (searchRequest.getTags() != null && !searchRequest.getTags().isEmpty()) {
            criteriaList.add(Criteria.where("tags").in(searchRequest.getTags()));
            log.debug("Added tags filter for: {}", searchRequest.getTags());
        }
        
        // Filter by siteId if provided
        if (searchRequest.getSiteId() != null && !searchRequest.getSiteId().trim().isEmpty()) {
            criteriaList.add(Criteria.where("siteId").is(searchRequest.getSiteId()));
            log.debug("Added siteId filter for: {}", searchRequest.getSiteId());
        }
        
        // Geospatial search
        if (searchRequest.hasGeospatialParams()) {
            criteriaList.add(addGeospatialCriteria(searchRequest));
        }
        
        // Build the final query
        Query query = new Query();
        if (!criteriaList.isEmpty()) {
            Criteria[] criteriaArray = criteriaList.toArray(new Criteria[criteriaList.size()]);
            query.addCriteria(new Criteria().andOperator(criteriaArray));
        }
        
        List<Location> locations = mongoTemplate.find(query, Location.class);
        log.debug("Found {} locations matching search criteria", locations.size());
        
        return locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Add geospatial search criteria
     * @param searchRequest the search request with geospatial parameters
     * @return Criteria for geospatial search
     */
    private Criteria addGeospatialCriteria(LocationSearchRequest searchRequest) {
        log.debug("Adding geospatial search criteria: lat={}, lng={}, radius={}m", 
                 searchRequest.getLat(), searchRequest.getLng(), searchRequest.getRadius());
        
        // For geospatial search, we need to check if the location has a node with coordinates
        // and if those coordinates are within the specified radius
        
        // First, ensure the location has a node with coordinates
        Criteria nodeExistsCriteria = new Criteria().andOperator(
            Criteria.where("node.lat").exists(true),
            Criteria.where("node.lng").exists(true)
        );
        
        // Calculate the approximate bounding box for performance
        // This is a rough approximation: 1 degree ≈ 111,000 meters
        double radiusInDegrees = searchRequest.getRadius() / 111000.0;
        double minLat = searchRequest.getLat() - radiusInDegrees;
        double maxLat = searchRequest.getLat() + radiusInDegrees;
        double minLng = searchRequest.getLng() - radiusInDegrees;
        double maxLng = searchRequest.getLng() + radiusInDegrees;
        
        Criteria boundingBoxCriteria = new Criteria().andOperator(
            Criteria.where("node.lat").gte(minLat).lte(maxLat),
            Criteria.where("node.lng").gte(minLng).lte(maxLng)
        );
        
        return new Criteria().andOperator(nodeExistsCriteria, boundingBoxCriteria);
    }
    
    /**
     * Get all locations (fallback method)
     * @return List of all LocationDto
     */
    private List<LocationDto> getAllLocations() {
        List<Location> locations = locationRepository.findAll();
        return locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
    }
}
