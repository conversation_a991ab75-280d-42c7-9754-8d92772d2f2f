package com.demo.mcp.service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.demo.mcp.mapper.CategoryMapper;
import com.demo.mcp.model.document.Category;
import com.demo.mcp.model.response.CategoryDto;
import com.demo.mcp.repository.CategoryRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class CategoryService {

    private final CategoryRepository categoryRepository;
    private final CategoryMapper categoryMapper;

    public List<CategoryDto> getAllCategories() {
        List<Category> categories = categoryRepository.findAll();
        return categories.stream()
                .map(categoryMapper::toCategoryDto)
                .collect(Collectors.toList());
    }

    public List<CategoryDto> getCategoriesBySiteId(String siteId) {
        List<Category> categories = categoryRepository.findBySiteId(siteId);
        return categories.stream()
                .map(categoryMapper::toCategoryDto)
                .collect(Collectors.toList());
    }

    public Optional<CategoryDto> getCategoryById(String categoryId) {
        return categoryRepository.findByCategoryId(categoryId)
                .map(categoryMapper::toCategoryDto);
    }

    public List<CategoryDto> searchCategoriesByName(String name) {
        List<Category> categories = categoryRepository.findByNameContainingIgnoreCase(name);
        return categories.stream()
                .map(categoryMapper::toCategoryDto)
                .collect(Collectors.toList());
    }
}
