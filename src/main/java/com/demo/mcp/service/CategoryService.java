package com.demo.mcp.service;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.demo.mcp.model.document.Category;
import com.demo.mcp.repository.CategoryRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class CategoryService {

    private final CategoryRepository categoryRepository;

    public List<Category> getAllCategories() {
        return categoryRepository.findAll();
    }

    public List<Category> getCategoriesBySiteId(String siteId) {
        return categoryRepository.findBySiteId(siteId);
    }

    public Optional<Category> getCategoryById(String categoryId) {
        return categoryRepository.findByCategoryId(categoryId);
    }

    public List<Category> searchCategoriesByName(String name) {
        return categoryRepository.findByNameContainingIgnoreCase(name);
    }
}
