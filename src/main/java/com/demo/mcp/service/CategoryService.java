package com.demo.mcp.service;

import com.demo.mcp.mapper.CategoryMapper;
import com.demo.mcp.model.document.Category;
import com.demo.mcp.model.response.CategoryDto;
import com.demo.mcp.repository.CategoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CategoryService {
    
    private final CategoryRepository categoryRepository;
    private final CategoryMapper categoryMapper;
    
    /**
     * Retrieve all categories
     * @return List of CategoryDto
     */
    public List<CategoryDto> getAllCategories() {
        log.debug("Retrieving all categories");
        List<Category> categories = categoryRepository.findAll();
        return categories.stream()
                .map(categoryMapper::toCategoryDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Retrieve categories by site ID
     * @param siteId the site ID
     * @return List of CategoryDto for the site
     */
    public List<CategoryDto> getCategoriesBySiteId(String siteId) {
        log.debug("Retrieving categories for siteId: {}", siteId);
        List<Category> categories = categoryRepository.findBySiteId(siteId);
        return categories.stream()
                .map(categoryMapper::toCategoryDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Retrieve a category by ID
     * @param categoryId the category ID
     * @return Optional containing CategoryDto if found
     */
    public Optional<CategoryDto> getCategoryById(String categoryId) {
        log.debug("Retrieving category with id: {}", categoryId);
        return categoryRepository.findByCategoryId(categoryId)
                .map(categoryMapper::toCategoryDto);
    }
    
    /**
     * Search categories by name
     * @param name the name to search for
     * @return List of matching CategoryDto
     */
    public List<CategoryDto> searchCategoriesByName(String name) {
        log.debug("Searching categories with name containing: {}", name);
        List<Category> categories = categoryRepository.findByNameContainingIgnoreCase(name);
        return categories.stream()
                .map(categoryMapper::toCategoryDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Check if a category exists
     * @param categoryId the category ID
     * @return true if exists, false otherwise
     */
    public boolean categoryExists(String categoryId) {
        return categoryRepository.existsByCategoryId(categoryId);
    }
}
