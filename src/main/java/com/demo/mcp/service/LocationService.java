package com.demo.mcp.service;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.demo.mcp.model.document.Location;
import com.demo.mcp.repository.LocationRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LocationService {

    private final LocationRepository locationRepository;

    public List<Location> getAllLocations() {
        return locationRepository.findAll();
    }

    public List<Location> getLocationsBySiteId(String siteId) {
        return locationRepository.findBySiteId(siteId);
    }

    public Optional<Location> getLocationById(String locationId) {
        return locationRepository.findByLocationId(locationId);
    }

    public List<Location> searchLocationsByName(String name) {
        return locationRepository.findByNameContainingIgnoreCase(name);
    }
}
