package com.demo.mcp.service;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.demo.mcp.model.document.Location;
import com.demo.mcp.repository.LocationRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LocationService {

    private final LocationRepository locationRepository;

    public List<Location> getAllLocations() {
        return locationRepository.findAll();
    }

    public List<Location> getLocationsBySiteId(String siteId) {
        return locationRepository.findBySiteId(siteId);
    }

    public Optional<Location> getLocationById(String locationId) {
        return locationRepository.findByLocationId(locationId);
    }

    public List<Location> searchLocationsByName(String name) {
        return locationRepository.findByNameContainingIgnoreCase(name);
    }

    /**
     * Search locations with comprehensive criteria
     * @param query Text search for location names (optional)
     * @param category Filter by category IDs (optional, array)
     * @param tags Filter by tags (optional, array)
     * @param lat Latitude for geospatial search (optional)
     * @param lng Longitude for geospatial search (optional)
     * @param radius Search radius in meters for geospatial search (optional)
     * @return List of matching locations
     */
    public List<Location> searchLocations(String query, List<String> category, List<String> tags,
                                         Double lat, Double lng, Double radius) {

        // For geospatial search, we would need to implement geospatial queries
        // For now, we'll focus on text, category, and tag searches
        if (lat != null && lng != null && radius != null) {
            // TODO: Implement geospatial search using MongoDB geospatial queries
            // For now, fall back to text/category/tag search
        }

        // Use MongoRepository methods directly for search
        if (query != null && !query.trim().isEmpty() &&
                category != null && !category.isEmpty() &&
                tags != null && !tags.isEmpty()) {
            return locationRepository.findBySearchCriteria(query.trim(), category, tags);
        } else if (query != null && !query.trim().isEmpty()) {
            return locationRepository.findByNameContainingIgnoreCase(query.trim());
        } else if (category != null && !category.isEmpty()) {
            return locationRepository.findByCategoriesIn(category);
        } else if (tags != null && !tags.isEmpty()) {
            return locationRepository.findByTagsIn(tags);
        } else {
            return locationRepository.findAll();
        }
    }
}
