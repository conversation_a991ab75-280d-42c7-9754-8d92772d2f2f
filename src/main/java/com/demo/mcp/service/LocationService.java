package com.demo.mcp.service;

import com.demo.mcp.mapper.LocationMapper;
import com.demo.mcp.model.document.Location;
import com.demo.mcp.model.response.LocationDto;
import com.demo.mcp.repository.LocationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class LocationService {
    
    private final LocationRepository locationRepository;
    private final LocationMapper locationMapper;
    
    /**
     * Retrieve all locations
     * @return List of LocationDto
     */
    public List<LocationDto> getAllLocations() {
        log.debug("Retrieving all locations");
        List<Location> locations = locationRepository.findAll();
        return locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Retrieve locations by site ID
     * @param siteId the site ID
     * @return List of LocationDto for the site
     */
    public List<LocationDto> getLocationsBySiteId(String siteId) {
        log.debug("Retrieving locations for siteId: {}", siteId);
        List<Location> locations = locationRepository.findBySiteId(siteId);
        return locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Retrieve a location by ID
     * @param locationId the location ID
     * @return Optional containing LocationDto if found
     */
    public Optional<LocationDto> getLocationById(String locationId) {
        log.debug("Retrieving location with id: {}", locationId);
        return locationRepository.findByLocationId(locationId)
                .map(locationMapper::toLocationDto);
    }
    
    /**
     * Search locations by name
     * @param name the name to search for
     * @return List of matching LocationDto
     */
    public List<LocationDto> searchLocationsByName(String name) {
        log.debug("Searching locations with name containing: {}", name);
        List<Location> locations = locationRepository.findByNameContainingIgnoreCase(name);
        return locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Find locations by categories
     * @param categoryIds list of category IDs
     * @return List of matching LocationDto
     */
    public List<LocationDto> getLocationsByCategories(List<String> categoryIds) {
        log.debug("Retrieving locations for categories: {}", categoryIds);
        List<Location> locations = locationRepository.findByCategoriesIn(categoryIds);
        return locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Find locations by tags
     * @param tags list of tags
     * @return List of matching LocationDto
     */
    public List<LocationDto> getLocationsByTags(List<String> tags) {
        log.debug("Retrieving locations for tags: {}", tags);
        List<Location> locations = locationRepository.findByTagsIn(tags);
        return locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Check if a location exists
     * @param locationId the location ID
     * @return true if exists, false otherwise
     */
    public boolean locationExists(String locationId) {
        return locationRepository.existsByLocationId(locationId);
    }
}
