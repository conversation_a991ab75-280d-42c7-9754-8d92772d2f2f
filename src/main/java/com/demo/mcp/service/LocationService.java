package com.demo.mcp.service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.demo.mcp.mapper.LocationMapper;
import com.demo.mcp.model.document.Location;
import com.demo.mcp.model.response.LocationDto;
import com.demo.mcp.repository.LocationRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LocationService {

    private final LocationRepository locationRepository;
    private final LocationMapper locationMapper;

    public List<LocationDto> getAllLocations() {
        List<Location> locations = locationRepository.findAll();
        return locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
    }

    public List<LocationDto> getLocationsBySiteId(String siteId) {
        List<Location> locations = locationRepository.findBySiteId(siteId);
        return locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
    }

    public Optional<LocationDto> getLocationById(String locationId) {
        return locationRepository.findByLocationId(locationId)
                .map(locationMapper::toLocationDto);
    }

    public List<LocationDto> searchLocationsByName(String name) {
        List<Location> locations = locationRepository.findByNameContainingIgnoreCase(name);
        return locations.stream()
                .map(locationMapper::toLocationDto)
                .collect(Collectors.toList());
    }
}
