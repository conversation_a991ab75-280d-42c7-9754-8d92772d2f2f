package com.demo.mcp.service;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.demo.mcp.model.document.Location;
import com.demo.mcp.model.request.LocationSearchRequest;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class McpToolService {

    private final ToolService toolService;

    /**
     * MCP Tool for searching locations with various criteria
     * This tool exposes the location search functionality to external MCP clients
     *
     * @param query Text search for location names (optional)
     * @param category Filter by category IDs (optional, array)
     * @param tags Filter by tags (optional, array)
     * @param lat Latitude for geospatial search (optional)
     * @param lng Longitude for geospatial search (optional)
     * @param radius Search radius in meters for geospatial search (optional)
     * @param siteId Filter by site ID (optional)
     * @return Map containing search results and metadata
     */
    public Map<String, Object> searchLocations(
            String query,
            List<String> category,
            List<String> tags,
            Double lat,
            Double lng,
            Double radius,
            String siteId) {

        log.info("MCP Tool beco_location_search called with parameters: query={}, category={}, tags={}, lat={}, lng={}, radius={}, siteId={}",
                query, category, tags, lat, lng, radius, siteId);

        // Create search request from MCP tool parameters
        LocationSearchRequest searchRequest = new LocationSearchRequest();
        searchRequest.setQuery(query);
        searchRequest.setCategory(category);
        searchRequest.setTags(tags);
        searchRequest.setLat(lat);
        searchRequest.setLng(lng);
        searchRequest.setRadius(radius);
        searchRequest.setSiteId(siteId);

        // Validate geospatial parameters
        if (isPartialGeospatialParams(lat, lng, radius)) {
            log.warn("Incomplete geospatial parameters provided. All of lat, lng, and radius must be provided together.");
            return Map.of(
                "success", false,
                "error", "Incomplete geospatial parameters. All of lat, lng, and radius must be provided together.",
                "locations", List.of(),
                "count", 0
            );
        }

        // Validate radius if provided
        if (radius != null && radius <= 0) {
            log.warn("Invalid radius value: {}. Radius must be positive.", radius);
            return Map.of(
                "success", false,
                "error", "Invalid radius value. Radius must be positive.",
                "locations", List.of(),
                "count", 0
            );
        }

        try {
            // Perform the search using the existing ToolService
            List<Location> locations = toolService.searchLocations(searchRequest);

            log.info("MCP Tool search completed successfully. Found {} locations", locations.size());

            // Return structured response for MCP clients
            return Map.of(
                "success", true,
                "locations", locations,
                "count", locations.size(),
                "searchCriteria", Map.of(
                    "query", query != null ? query : "",
                    "category", category != null ? category : List.of(),
                    "tags", tags != null ? tags : List.of(),
                    "geospatial", searchRequest.hasGeospatialParams(),
                    "siteId", siteId != null ? siteId : ""
                )
            );

        } catch (Exception e) {
            log.error("Error performing MCP location search", e);
            return Map.of(
                "success", false,
                "error", "An error occurred while searching locations: " + e.getMessage(),
                "locations", List.of(),
                "count", 0
            );
        }
    }

    /**
     * Check if geospatial parameters are partially provided
     * @param lat latitude
     * @param lng longitude
     * @param radius radius
     * @return true if some but not all geospatial parameters are provided
     */
    private boolean isPartialGeospatialParams(Double lat, Double lng, Double radius) {
        int providedParams = 0;
        if (lat != null) providedParams++;
        if (lng != null) providedParams++;
        if (radius != null) providedParams++;

        return providedParams > 0 && providedParams < 3;
    }
}
