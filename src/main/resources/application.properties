spring.application.name=becomap-mcp

APP_PORT=8081
APP_PROFILE=staging
server.port=8081

spring.data.mongodb.uri=mongodb+srv://gokuldas:<EMAIL>/?retryWrites=true&w=majority&appName=test

spring.data.mongodb.database=beco_lighting_db
spring.data.mongodb.auto-index-creation=true

# MCP Server Configuration
spring.ai.mcp.server.enabled=true
spring.ai.mcp.server.name=becomap-mcp
spring.ai.mcp.server.version=1.0.0
spring.ai.mcp.server.description=BecoMap MCP server providing location search functionality for Beco lighting system
spring.ai.mcp.server.transport.sse.endpoint=/mcp
