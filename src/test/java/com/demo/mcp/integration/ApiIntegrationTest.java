package com.demo.mcp.integration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureWebMvc
@TestPropertySource(properties = {
    "spring.data.mongodb.uri=mongodb://localhost:27017/test_db",
    "spring.data.mongodb.auto-index-creation=false"
})
class ApiIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Test
    void contextLoads() {
        // This test ensures that the Spring context loads successfully
        // with all our new components
    }

    @Test
    void categoriesEndpoint_ShouldBeAccessible() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // This test verifies that the categories endpoint is accessible
        // It may return empty results if no data exists, but should not fail
        mockMvc.perform(get("/categories"))
                .andExpect(status().isOk());
    }

    @Test
    void locationsEndpoint_ShouldBeAccessible() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // This test verifies that the locations endpoint is accessible
        mockMvc.perform(get("/locations"))
                .andExpect(status().isOk());
    }

    @Test
    void toolsLocationSearchEndpoint_ShouldBeAccessible() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // This test verifies that the tools location search endpoint is accessible
        mockMvc.perform(get("/tools/locations/search"))
                .andExpect(status().isOk());
    }
}
