package com.demo.mcp.controller;

import com.demo.mcp.model.response.CategoryDto;
import com.demo.mcp.service.CategoryService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(CategoryController.class)
class CategoryControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CategoryService categoryService;

    @Test
    void getAllCategories_ShouldReturnAllCategories() throws Exception {
        // Given
        CategoryDto category1 = createTestCategoryDto("cat1", "Category 1");
        CategoryDto category2 = createTestCategoryDto("cat2", "Category 2");
        List<CategoryDto> categories = Arrays.asList(category1, category2);

        when(categoryService.getAllCategories()).thenReturn(categories);

        // When & Then
        mockMvc.perform(get("/categories"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].categoryId").value("cat1"))
                .andExpect(jsonPath("$[0].name").value("Category 1"))
                .andExpect(jsonPath("$[1].categoryId").value("cat2"))
                .andExpect(jsonPath("$[1].name").value("Category 2"));
    }

    @Test
    void getAllCategories_WithSiteId_ShouldReturnCategoriesForSite() throws Exception {
        // Given
        CategoryDto category = createTestCategoryDto("cat1", "Category 1");
        List<CategoryDto> categories = Arrays.asList(category);

        when(categoryService.getCategoriesBySiteId("site1")).thenReturn(categories);

        // When & Then
        mockMvc.perform(get("/categories").param("siteId", "site1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].categoryId").value("cat1"));
    }

    @Test
    void getCategoryById_WhenExists_ShouldReturnCategory() throws Exception {
        // Given
        CategoryDto category = createTestCategoryDto("cat1", "Category 1");
        when(categoryService.getCategoryById("cat1")).thenReturn(Optional.of(category));

        // When & Then
        mockMvc.perform(get("/categories/cat1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.categoryId").value("cat1"))
                .andExpect(jsonPath("$.name").value("Category 1"));
    }

    @Test
    void getCategoryById_WhenNotExists_ShouldReturn404() throws Exception {
        // Given
        when(categoryService.getCategoryById("nonexistent")).thenReturn(Optional.empty());

        // When & Then
        mockMvc.perform(get("/categories/nonexistent"))
                .andExpect(status().isNotFound());
    }

    @Test
    void getCategoryById_WithEmptyId_ShouldReturn400() throws Exception {
        // When & Then
        mockMvc.perform(get("/categories/ "))
                .andExpect(status().isBadRequest());
    }

    @Test
    void searchCategories_ShouldReturnMatchingCategories() throws Exception {
        // Given
        CategoryDto category = createTestCategoryDto("cat1", "Test Category");
        List<CategoryDto> categories = Arrays.asList(category);

        when(categoryService.searchCategoriesByName("Test")).thenReturn(categories);

        // When & Then
        mockMvc.perform(get("/categories/search").param("name", "Test"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].name").value("Test Category"));
    }

    @Test
    void searchCategories_WithEmptyName_ShouldReturn400() throws Exception {
        // When & Then
        mockMvc.perform(get("/categories/search").param("name", ""))
                .andExpect(status().isBadRequest());
    }

    private CategoryDto createTestCategoryDto(String categoryId, String name) {
        CategoryDto dto = new CategoryDto();
        dto.setCategoryId(categoryId);
        dto.setName(name);
        dto.setCreatedDateTime(Instant.now());
        dto.setUpdatedDateTime(Instant.now());
        dto.setSiteId("test-site");
        return dto;
    }
}
