# MCP Location Search Server - Claude <PERSON>op Integration

This Spring Boot application serves as both a REST API and an MCP (Model Context Protocol) server for location search functionality. It can be integrated with <PERSON> to provide location search capabilities directly within your Claude conversations.

## 🚀 Quick Start

### Prerequisites
- Java 17 or higher
- Maven 3.6+
- MongoDB Atlas connection (configured in `application.properties`)

### 1. Start the MCP Server

```bash
cd /path/to/your/mcp/project
mvn spring-boot:run
```

The server will start on **http://localhost:8081**

You should see these logs confirming MCP server is ready:
```
INFO 102080 --- [becomap-mcp] [main] o.s.a.m.s.a.McpServerAutoConfiguration : Enable tools capabilities, notification: true
INFO 102080 --- [becomap-mcp] [main] o.s.a.m.s.a.McpServerAutoConfiguration : Enable resources capabilities, notification: true
INFO 102080 --- [becomap-mcp] [main] o.s.a.m.s.a.McpServerAutoConfiguration : Enable prompts capabilities, notification: true
INFO 102080 --- [becomap-mcp] [main] o.s.a.m.s.a.McpServerAutoConfiguration : Enable completions capabilities
INFO 102080 --- [becomap-mcp] [main] o.s.b.w.embedded.tomcat.TomcatWebServer : Tomcat started on port 8081 (http)
```

Notice the `[becomap-mcp]` in the logs, confirming the server name is properly configured.

### 2. Configure Claude Desktop

Add the following configuration to your Claude Desktop MCP settings:

**Location**: `~/Library/Application Support/Claude/claude_desktop_config.json` (macOS) or equivalent for your OS.

**Note**: We use `becomap-mcp` as the server name to distinguish it from other MCP servers you might have configured.

```json
{
  "mcpServers": {
    "becomap-mcp": {
      "command": "curl",
      "args": [
        "-X", "POST",
        "http://localhost:8081/mcp",
        "-H", "Content-Type: application/json",
        "-d", "@-"
      ],
      "env": {}
    }
  }
}
```

### 3. Restart Claude Desktop

After adding the configuration, restart Claude Desktop to load the new MCP server.

## 🔧 Available Tools

### `beco_location_search`

Search for locations by name, category, and tags within a specific site.

**Parameters:**
- `siteId` (required): Site ID to search within
- `query` (optional): Text search for location names
- `category` (optional): Array of category names to filter by
- `tags` (optional): Array of tags to filter by

**Example Usage in Claude:**

```
Search for conference rooms in site 6762917d8489ac3bc9175d0c
```

```
Find locations with "meeting" in the name for site 6762917d8489ac3bc9175d0c
```

```
Show me all locations tagged with "wifi" in site 6762917d8489ac3bc9175d0c
```

## 🌐 REST API Endpoints

The server also provides traditional REST API endpoints:

### Location Search
```
GET /locations/search?siteId={siteId}&query={query}&category={category}&tags={tags}
```

**Example:**
```bash
curl "http://localhost:8081/locations/search?siteId=6762917d8489ac3bc9175d0c&query=test"
```

### Get All Locations
```
GET /locations
```

### Get Location by ID
```
GET /locations/{locationId}
```

## ⚠️ Error Handling

The API returns structured error responses for validation issues:

```json
{
  "error": {
    "code": 422,
    "message": "Invalid siteId - site does not exist",
    "validation": {
      "siteId": "invalid-site-id"
    }
  }
}
```

## 🔍 Testing the Integration

1. **Test the server is running:**
   ```bash
   curl http://localhost:8081/locations
   ```

2. **Test location search:**
   ```bash
   curl "http://localhost:8081/locations/search?siteId=6762917d8489ac3bc9175d0c&query=test"
   ```

3. **Test error handling:**
   ```bash
   curl "http://localhost:8081/locations/search?siteId=invalid"
   ```

## 📝 Sample Data

The application comes with sample location data. Use siteId `6762917d8489ac3bc9175d0c` for testing.

## 🛠️ Troubleshooting

### MCP Server Not Connecting
- Ensure the Spring Boot application is running on port 8081
- Check Claude Desktop logs for connection errors
- Verify the MCP configuration in `claude_desktop_config.json`

### No Search Results
- Verify the siteId exists in your database
- Check the MongoDB connection in `application.properties`
- Review server logs for any errors

### Tool Not Available in Claude
- Restart Claude Desktop after configuration changes
- Check that the MCP server configuration is valid JSON
- Ensure the server is accessible at `http://localhost:8081`

## 🎯 Architecture

- **ToolController**: Handles REST API endpoints
- **ToolService**: Contains `@Tool` annotated methods for MCP integration
- **MongoDB**: Stores location data with site-based organization
- **Spring AI MCP**: Provides MCP server capabilities

The same service method serves both REST API calls and MCP tool invocations, ensuring consistency across interfaces.
